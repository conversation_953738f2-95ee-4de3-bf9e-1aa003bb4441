{"name": "payment-website", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "server": "node server/index.js", "server:dev": "nodemon server/index.js", "dev:full": "concurrently \"npm run server:dev\" \"npm run dev\""}, "dependencies": {"react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "^6.28.0", "axios": "^1.6.2", "qrcode": "^1.5.3"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "vite": "^6.3.5", "concurrently": "^8.2.2", "nodemon": "^3.0.2"}}