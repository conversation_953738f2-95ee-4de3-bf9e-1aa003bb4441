import express from 'express';
import { pool } from '../config/database.js';
import { validateOrderId } from '../middleware/validation.js';

const router = express.Router();

// Get order details
router.get('/:orderId', validateOrderId, async (req, res) => {
  try {
    const { orderId } = req.params;
    
    const result = await pool.query(`
      SELECT 
        o.id,
        o.order_number,
        o.total_amount,
        o.currency,
        o.status as order_status,
        o.items,
        o.created_at,
        c.first_name,
        c.last_name,
        c.email,
        c.phone,
        c.address,
        c.city,
        c.state,
        c.pincode,
        p.razorpay_order_id,
        p.razorpay_payment_id,
        p.status as payment_status,
        p.payment_method,
        p.qr_code_url
      FROM orders o
      JOIN customers c ON o.customer_id = c.id
      LEFT JOIN payments p ON o.id = p.order_id
      WHERE o.id = $1
    `, [orderId]);
    
    if (result.rows.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'Order not found'
      });
    }
    
    const order = result.rows[0];
    
    res.json({
      success: true,
      data: {
        order: {
          id: order.id,
          orderNumber: order.order_number,
          totalAmount: parseFloat(order.total_amount),
          currency: order.currency,
          status: order.order_status,
          items: order.items,
          createdAt: order.created_at
        },
        customer: {
          firstName: order.first_name,
          lastName: order.last_name,
          email: order.email,
          phone: order.phone,
          address: order.address,
          city: order.city,
          state: order.state,
          pincode: order.pincode
        },
        payment: {
          razorpayOrderId: order.razorpay_order_id,
          razorpayPaymentId: order.razorpay_payment_id,
          status: order.payment_status,
          method: order.payment_method,
          qrCodeUrl: order.qr_code_url
        }
      }
    });
    
  } catch (error) {
    console.error('Get order error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get order details'
    });
  }
});

// Get orders by customer email
router.get('/customer/:email', async (req, res) => {
  try {
    const { email } = req.params;
    const { page = 1, limit = 10 } = req.query;
    
    const offset = (page - 1) * limit;
    
    const result = await pool.query(`
      SELECT 
        o.id,
        o.order_number,
        o.total_amount,
        o.currency,
        o.status as order_status,
        o.created_at,
        p.status as payment_status,
        p.payment_method
      FROM orders o
      JOIN customers c ON o.customer_id = c.id
      LEFT JOIN payments p ON o.id = p.order_id
      WHERE c.email = $1
      ORDER BY o.created_at DESC
      LIMIT $2 OFFSET $3
    `, [email, limit, offset]);
    
    // Get total count
    const countResult = await pool.query(`
      SELECT COUNT(*) as total
      FROM orders o
      JOIN customers c ON o.customer_id = c.id
      WHERE c.email = $1
    `, [email]);
    
    const total = parseInt(countResult.rows[0].total);
    const totalPages = Math.ceil(total / limit);
    
    res.json({
      success: true,
      data: {
        orders: result.rows.map(order => ({
          id: order.id,
          orderNumber: order.order_number,
          totalAmount: parseFloat(order.total_amount),
          currency: order.currency,
          orderStatus: order.order_status,
          paymentStatus: order.payment_status,
          paymentMethod: order.payment_method,
          createdAt: order.created_at
        })),
        pagination: {
          currentPage: parseInt(page),
          totalPages,
          totalOrders: total,
          hasNext: page < totalPages,
          hasPrev: page > 1
        }
      }
    });
    
  } catch (error) {
    console.error('Get customer orders error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get customer orders'
    });
  }
});

// Update order status (admin endpoint)
router.patch('/:orderId/status', validateOrderId, async (req, res) => {
  try {
    const { orderId } = req.params;
    const { status } = req.body;
    
    const validStatuses = ['pending', 'paid', 'failed', 'cancelled', 'refunded'];
    if (!validStatuses.includes(status)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid status'
      });
    }
    
    const result = await pool.query(`
      UPDATE orders 
      SET status = $1, updated_at = CURRENT_TIMESTAMP
      WHERE id = $2
      RETURNING *
    `, [status, orderId]);
    
    if (result.rows.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'Order not found'
      });
    }
    
    res.json({
      success: true,
      data: {
        orderId: result.rows[0].id,
        status: result.rows[0].status,
        updatedAt: result.rows[0].updated_at
      }
    });
    
  } catch (error) {
    console.error('Update order status error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to update order status'
    });
  }
});

// Get order statistics (admin endpoint)
router.get('/stats/summary', async (req, res) => {
  try {
    const result = await pool.query(`
      SELECT 
        COUNT(*) as total_orders,
        COUNT(CASE WHEN status = 'paid' THEN 1 END) as paid_orders,
        COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_orders,
        COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_orders,
        COALESCE(SUM(CASE WHEN status = 'paid' THEN total_amount END), 0) as total_revenue,
        COALESCE(AVG(CASE WHEN status = 'paid' THEN total_amount END), 0) as avg_order_value
      FROM orders
      WHERE created_at >= CURRENT_DATE - INTERVAL '30 days'
    `);
    
    const stats = result.rows[0];
    
    res.json({
      success: true,
      data: {
        totalOrders: parseInt(stats.total_orders),
        paidOrders: parseInt(stats.paid_orders),
        pendingOrders: parseInt(stats.pending_orders),
        failedOrders: parseInt(stats.failed_orders),
        totalRevenue: parseFloat(stats.total_revenue),
        avgOrderValue: parseFloat(stats.avg_order_value),
        period: 'Last 30 days'
      }
    });
    
  } catch (error) {
    console.error('Get order stats error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get order statistics'
    });
  }
});

export default router;
