import Joi from 'joi';

// Customer data validation schema
const customerSchema = Joi.object({
  firstName: Joi.string().min(2).max(50).required(),
  lastName: Joi.string().min(2).max(50).required(),
  email: Joi.string().email().required(),
  phone: Joi.string().pattern(/^[6-9]\d{9}$/).required().messages({
    'string.pattern.base': 'Phone number must be a valid Indian mobile number'
  }),
  address: Joi.string().min(10).max(200).required(),
  city: Joi.string().min(2).max(50).required(),
  state: Joi.string().min(2).max(50).required(),
  pincode: Joi.string().pattern(/^\d{6}$/).required().messages({
    'string.pattern.base': 'Pincode must be a 6-digit number'
  })
});

// Order data validation schema
const orderSchema = Joi.object({
  amount: Joi.number().positive().precision(2).required(),
  currency: Joi.string().valid('INR').default('INR')
});

// Cart items validation schema
const itemSchema = Joi.object({
  id: Joi.number().integer().positive().required(),
  name: Joi.string().min(1).max(100).required(),
  price: Joi.number().positive().precision(2).required(),
  quantity: Joi.number().integer().positive().required(),
  image: Joi.string().uri().optional()
});

const itemsSchema = Joi.array().items(itemSchema).min(1).required();

// Payment data validation middleware
export const validatePaymentData = (req, res, next) => {
  const { customerData, orderData, items } = req.body;

  // Validate customer data
  const { error: customerError } = customerSchema.validate(customerData);
  if (customerError) {
    return res.status(400).json({
      success: false,
      error: 'Invalid customer data',
      details: customerError.details[0].message
    });
  }

  // Validate order data
  const { error: orderError } = orderSchema.validate(orderData);
  if (orderError) {
    return res.status(400).json({
      success: false,
      error: 'Invalid order data',
      details: orderError.details[0].message
    });
  }

  // Validate items
  const { error: itemsError } = itemsSchema.validate(items);
  if (itemsError) {
    return res.status(400).json({
      success: false,
      error: 'Invalid items data',
      details: itemsError.details[0].message
    });
  }

  // Verify total amount matches items
  const calculatedTotal = items.reduce((total, item) => {
    return total + (item.price * item.quantity);
  }, 0);

  if (Math.abs(calculatedTotal - orderData.amount) > 0.01) {
    return res.status(400).json({
      success: false,
      error: 'Order amount does not match items total'
    });
  }

  next();
};

// Webhook validation middleware
export const validateWebhookData = (req, res, next) => {
  const signature = req.headers['x-razorpay-signature'];
  
  if (!signature) {
    return res.status(400).json({
      success: false,
      error: 'Missing webhook signature'
    });
  }

  req.webhookSignature = signature;
  next();
};

// Payment verification validation
export const validatePaymentVerification = (req, res, next) => {
  const schema = Joi.object({
    razorpay_order_id: Joi.string().required(),
    razorpay_payment_id: Joi.string().required(),
    razorpay_signature: Joi.string().required()
  });

  const { error } = schema.validate(req.body);
  if (error) {
    return res.status(400).json({
      success: false,
      error: 'Invalid payment verification data',
      details: error.details[0].message
    });
  }

  next();
};

// Order ID validation
export const validateOrderId = (req, res, next) => {
  const { orderId } = req.params;
  
  if (!orderId || isNaN(orderId)) {
    return res.status(400).json({
      success: false,
      error: 'Invalid order ID'
    });
  }

  next();
};
