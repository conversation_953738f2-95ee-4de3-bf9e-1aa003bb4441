import Razorpay from 'razorpay';
import crypto from 'crypto';
import dotenv from 'dotenv';

dotenv.config();

// Initialize Razorpay instance
export const razorpay = new Razorpay({
  key_id: process.env.RAZORPAY_KEY_ID,
  key_secret: process.env.RAZORPAY_KEY_SECRET,
});

// Verify Razorpay configuration
export function verifyRazorpayConfig() {
  if (!process.env.RAZORPAY_KEY_ID || !process.env.RAZORPAY_KEY_SECRET) {
    throw new Error('Razorpay credentials not configured. Please set RAZORPAY_KEY_ID and RAZORPAY_KEY_SECRET in environment variables.');
  }
  console.log('✅ Razorpay configuration verified');
}

// Create Razorpay order
export async function createRazorpayOrder(orderData) {
  try {
    const options = {
      amount: Math.round(orderData.amount * 100), // Convert to paise
      currency: orderData.currency || 'INR',
      receipt: orderData.receipt,
      notes: orderData.notes || {},
      payment_capture: 1, // Auto capture payment
    };

    const order = await razorpay.orders.create(options);
    console.log('✅ Razorpay order created:', order.id);
    return order;
  } catch (error) {
    console.error('❌ Failed to create Razorpay order:', error);
    throw new Error(`Failed to create payment order: ${error.message}`);
  }
}

// Verify payment signature
export function verifyPaymentSignature(paymentData) {
  try {
    const { razorpay_order_id, razorpay_payment_id, razorpay_signature } = paymentData;
    
    const body = razorpay_order_id + '|' + razorpay_payment_id;
    const expectedSignature = crypto
      .createHmac('sha256', process.env.RAZORPAY_KEY_SECRET)
      .update(body.toString())
      .digest('hex');

    const isValid = expectedSignature === razorpay_signature;
    console.log(isValid ? '✅ Payment signature verified' : '❌ Invalid payment signature');
    return isValid;
  } catch (error) {
    console.error('❌ Payment signature verification failed:', error);
    return false;
  }
}

// Verify webhook signature
export function verifyWebhookSignature(body, signature) {
  try {
    const expectedSignature = crypto
      .createHmac('sha256', process.env.RAZORPAY_WEBHOOK_SECRET)
      .update(JSON.stringify(body))
      .digest('hex');

    const isValid = expectedSignature === signature;
    console.log(isValid ? '✅ Webhook signature verified' : '❌ Invalid webhook signature');
    return isValid;
  } catch (error) {
    console.error('❌ Webhook signature verification failed:', error);
    return false;
  }
}

// Get payment details
export async function getPaymentDetails(paymentId) {
  try {
    const payment = await razorpay.payments.fetch(paymentId);
    console.log('✅ Payment details fetched:', paymentId);
    return payment;
  } catch (error) {
    console.error('❌ Failed to fetch payment details:', error);
    throw new Error(`Failed to fetch payment details: ${error.message}`);
  }
}

// Refund payment
export async function refundPayment(paymentId, amount) {
  try {
    const refund = await razorpay.payments.refund(paymentId, {
      amount: Math.round(amount * 100), // Convert to paise
    });
    console.log('✅ Refund processed:', refund.id);
    return refund;
  } catch (error) {
    console.error('❌ Failed to process refund:', error);
    throw new Error(`Failed to process refund: ${error.message}`);
  }
}

// UPI payment options
export const UPI_APPS = {
  'googlepay': {
    name: 'Google Pay',
    package: 'com.google.android.apps.nbu.paisa.user',
    scheme: 'tez://'
  },
  'phonepe': {
    name: 'PhonePe',
    package: 'com.phonepe.app',
    scheme: 'phonepe://'
  },
  'paytm': {
    name: 'Paytm',
    package: 'net.one97.paytm',
    scheme: 'paytmmp://'
  },
  'bhim': {
    name: 'BHIM',
    package: 'in.org.npci.upiapp',
    scheme: 'bhim://'
  },
  'amazonpay': {
    name: 'Amazon Pay',
    package: 'in.amazon.mShop.android.shopping',
    scheme: 'amazonpay://'
  }
};

export default razorpay;
