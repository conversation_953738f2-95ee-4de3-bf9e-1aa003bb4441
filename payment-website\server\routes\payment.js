import express from 'express';
import QRCode from 'qrcode';
import { 
  createRazorpayOrder, 
  verifyPaymentSignature, 
  getPaymentDetails,
  UPI_APPS 
} from '../config/razorpay.js';
import { pool } from '../config/database.js';
import { validatePaymentData } from '../middleware/validation.js';

const router = express.Router();

// Create payment order
router.post('/create-order', validatePaymentData, async (req, res) => {
  const client = await pool.connect();
  
  try {
    await client.query('BEGIN');
    
    const { customerData, orderData, items } = req.body;
    
    // Insert or update customer
    const customerResult = await client.query(`
      INSERT INTO customers (first_name, last_name, email, phone, address, city, state, pincode)
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
      ON CONFLICT (email) 
      DO UPDATE SET 
        first_name = EXCLUDED.first_name,
        last_name = EXCLUDED.last_name,
        phone = EXCLUDED.phone,
        address = EXCLUDED.address,
        city = EXCLUDED.city,
        state = EXCLUDED.state,
        pincode = EXCLUDED.pincode,
        updated_at = CURRENT_TIMESTAMP
      RETURNING id
    `, [
      customerData.firstName,
      customerData.lastName,
      customerData.email,
      customerData.phone,
      customerData.address,
      customerData.city,
      customerData.state,
      customerData.pincode
    ]);
    
    const customerId = customerResult.rows[0].id;
    
    // Generate order number
    const orderNumber = `ORD-${Date.now()}-${Math.random().toString(36).substr(2, 9).toUpperCase()}`;
    
    // Create order in database
    const orderResult = await client.query(`
      INSERT INTO orders (customer_id, order_number, total_amount, items)
      VALUES ($1, $2, $3, $4)
      RETURNING id
    `, [customerId, orderNumber, orderData.amount, JSON.stringify(items)]);
    
    const orderId = orderResult.rows[0].id;
    
    // Create Razorpay order
    const razorpayOrder = await createRazorpayOrder({
      amount: orderData.amount,
      currency: 'INR',
      receipt: orderNumber,
      notes: {
        order_id: orderId,
        customer_email: customerData.email
      }
    });
    
    // Generate UPI payment string for QR code
    const upiString = `upi://pay?pa=${process.env.RAZORPAY_UPI_ID || 'merchant@upi'}&pn=PayShop&am=${orderData.amount}&cu=INR&tn=Payment for Order ${orderNumber}`;
    
    // Generate QR code
    const qrCodeDataURL = await QRCode.toDataURL(upiString, {
      width: 256,
      margin: 2,
      color: {
        dark: '#000000',
        light: '#FFFFFF'
      }
    });
    
    // Store payment record
    await client.query(`
      INSERT INTO payments (order_id, razorpay_order_id, amount, qr_code_url)
      VALUES ($1, $2, $3, $4)
    `, [orderId, razorpayOrder.id, orderData.amount, qrCodeDataURL]);
    
    await client.query('COMMIT');
    
    res.status(201).json({
      success: true,
      data: {
        orderId: orderId,
        orderNumber: orderNumber,
        razorpayOrderId: razorpayOrder.id,
        amount: orderData.amount,
        currency: 'INR',
        qrCode: qrCodeDataURL,
        upiApps: UPI_APPS,
        razorpayKeyId: process.env.RAZORPAY_KEY_ID
      }
    });
    
  } catch (error) {
    await client.query('ROLLBACK');
    console.error('Create order error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to create payment order'
    });
  } finally {
    client.release();
  }
});

// Verify payment
router.post('/verify', async (req, res) => {
  const client = await pool.connect();
  
  try {
    const { razorpay_order_id, razorpay_payment_id, razorpay_signature } = req.body;
    
    // Verify signature
    const isValid = verifyPaymentSignature({
      razorpay_order_id,
      razorpay_payment_id,
      razorpay_signature
    });
    
    if (!isValid) {
      return res.status(400).json({
        success: false,
        error: 'Invalid payment signature'
      });
    }
    
    // Get payment details from Razorpay
    const paymentDetails = await getPaymentDetails(razorpay_payment_id);
    
    // Update payment and order status
    await client.query('BEGIN');
    
    const paymentResult = await client.query(`
      UPDATE payments 
      SET razorpay_payment_id = $1, 
          razorpay_signature = $2, 
          status = $3,
          payment_method = $4,
          updated_at = CURRENT_TIMESTAMP
      WHERE razorpay_order_id = $5
      RETURNING order_id
    `, [
      razorpay_payment_id,
      razorpay_signature,
      paymentDetails.status,
      paymentDetails.method,
      razorpay_order_id
    ]);
    
    if (paymentResult.rows.length === 0) {
      throw new Error('Payment record not found');
    }
    
    const orderId = paymentResult.rows[0].order_id;
    
    // Update order status
    await client.query(`
      UPDATE orders 
      SET status = $1, updated_at = CURRENT_TIMESTAMP
      WHERE id = $2
    `, [paymentDetails.status === 'captured' ? 'paid' : 'failed', orderId]);
    
    await client.query('COMMIT');
    
    res.json({
      success: true,
      data: {
        paymentId: razorpay_payment_id,
        orderId: orderId,
        status: paymentDetails.status,
        amount: paymentDetails.amount / 100, // Convert from paise
        method: paymentDetails.method
      }
    });
    
  } catch (error) {
    await client.query('ROLLBACK');
    console.error('Payment verification error:', error);
    res.status(500).json({
      success: false,
      error: 'Payment verification failed'
    });
  } finally {
    client.release();
  }
});

// Get payment status
router.get('/status/:orderId', async (req, res) => {
  try {
    const { orderId } = req.params;
    
    const result = await pool.query(`
      SELECT 
        p.status as payment_status,
        p.razorpay_payment_id,
        p.payment_method,
        o.status as order_status,
        o.order_number,
        o.total_amount
      FROM payments p
      JOIN orders o ON p.order_id = o.id
      WHERE o.id = $1
    `, [orderId]);
    
    if (result.rows.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'Order not found'
      });
    }
    
    res.json({
      success: true,
      data: result.rows[0]
    });
    
  } catch (error) {
    console.error('Get payment status error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get payment status'
    });
  }
});

export default router;
